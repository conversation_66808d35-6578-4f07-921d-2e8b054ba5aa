package sk.isdd.dr.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;

@Getter
@Configuration
public class RecaptchaProperties {

    @Value("${google.recaptcha.secret-key}")
    private String secretKey;

    @Value("${google.recaptcha.site-key}")
    private String siteKey;

    @Value("${google.recaptcha.verify-url}")
    private String verifyUrl;

    @Value("${google.recaptcha.score-threshold}")
    private double scoreThreshold;

    @Value("${google.recaptcha.enabled}")
    private boolean enabled;

    @Value("${google.recaptcha.timeout-seconds}")
    private int timeoutSeconds;

//    @Value("${google.recaptcha.expected-action}")
//    private String expectedAction;
}
