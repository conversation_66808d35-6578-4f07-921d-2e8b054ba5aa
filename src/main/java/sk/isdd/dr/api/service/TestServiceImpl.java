package sk.isdd.dr.api.service;

import java.time.LocalDateTime;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.cebtalentcentral.AcknowledgeCandidateRegistration;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.CabinnetTest;
import sk.isdd.dr.api.dto.CabinnetTestParticipant;
import sk.isdd.dr.api.dto.request.StoreCabinnetResultRequest;
import sk.isdd.dr.api.dto.response.GetCabinnetTestParticipantsResponse;
import sk.isdd.dr.api.dto.response.StoreCabinnetResultResponse;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SHLService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.api.service.api.TestService;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.common.util.AuthUtils;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.entity.TestEntity;
import sk.isdd.dr.jpa.repository.TestRepository;

@Service
public class TestServiceImpl extends BaseService implements TestService {

    private final TestRepository testRepository;
    private final SubjectService subjectService;
    private final SHLService shlService;
    private final MainMapper mainMapper;
    private final ObjectMapper objectMapper;
    private final LogService auditLogger;

    public TestServiceImpl(TestRepository testRepository, SubjectService subjectService, SHLService shlService,
            ObjectMapper objectMapper, MainMapper mainMapper, LogService auditLogger) {
        this.testRepository = testRepository;
        this.subjectService = subjectService;
        this.shlService = shlService;
        this.objectMapper = objectMapper;
        this.mainMapper = mainMapper;
        this.auditLogger = auditLogger;
    }

    @Override
    @Transactional
    public StoreCabinnetResultResponse storeCabinnetTestResult(Integer subjectId, StoreCabinnetResultRequest request) {
        SubjectEntity subject = subjectService.loadSubject(subjectId);

        if (subject.getTest() != null) {
            throw new BadRequestException("Subject bad state");
        }

        LocalDateTime now = LocalDateTime.now();

        // register subject in SHL
        String shlTestCallbackHash = AuthUtils.convertToSHA256Hex(UUID.randomUUID().toString());
        String shlTestUri = "";
        Integer shlTestReceiptId = 0;
        try {
            AcknowledgeCandidateRegistration shlResponse = shlService
                    .registerCandidate(mainMapper.mapSubjectEntity(subject), request.isVerifyTest(),
                            shlTestCallbackHash);
            shlTestUri = shlResponse.getDataArea().getAssessmentOrder().getAssessmentAccess().getAssessment().getFirst()
                    .getAssessmentCommunication().getURI();
            shlTestReceiptId = (int) shlResponse.getAcknowledge().getReceiptId();
        } catch (RuntimeException e) {
            throw new SystemException("Unable to register subject in SHL");
        }

        // convert test result to JSON
        String testJson = "";
        try {
            request.getResult().setCreatedAt(now);
            testJson = objectMapper.writeValueAsString(request.getResult());
        } catch (Exception e) {
            throw new SystemException("Unable to convert test results to string");
        }

        TestEntity testEntity = new TestEntity();
        testEntity.setCreatedAt(now);
        testEntity.setSubject(subject);
        testEntity.setCabinnetResult(testJson);
        testEntity.setShlCallbackHash(shlTestCallbackHash);
        testEntity.setShlTestVerify(request.isVerifyTest());
        testEntity.setShlTestLink(shlTestUri);
        testEntity.setShlTestReceiptId(shlTestReceiptId);

        testRepository.save(testEntity);

        auditLogger.log(getCurrentUser().getId(), subject, LogAction.CABINNET_COMPLETE);

        return new StoreCabinnetResultResponse(shlTestUri);
    }

    @Override
    public GetCabinnetTestParticipantsResponse getCabinnetTestParticipants(LocalDateTime timeFrom) {
        return new GetCabinnetTestParticipantsResponse(testRepository.findByCreatedAtGreaterThan(timeFrom).stream()
                .map(test -> new CabinnetTestParticipant(
                        test.getSubject().getName() + " " + test.getSubject().getSurname(),
                        test.getSubject().getEmail()))
                .toList());
    }

    @Override
    @Transactional
    public CabinnetTest getCabinnetTestResults(String subjectEmail) {
        TestEntity test = loadTest(subjectEmail);
        auditLogger.log(getCurrentUser().getId(), test.getSubject(), LogAction.SHL_CABINNET_REQUESTED);

        try {
            return objectMapper.readValue(test.getCabinnetResult(), CabinnetTest.class);
        } catch (Exception e) {
            throw new SystemException("Cannot convert to CabinnetTest");
        }
    }

    private TestEntity loadTest(String subjectEmail) {
        return testRepository.findBySubjectEmail(subjectEmail)
                .orElseThrow(() -> new NotFoundException("Test for given email not found"));
    }

}
