package sk.isdd.dr.api.service;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import sk.isdd.dr.api.exception.CustomExceptionHandler;
import sk.isdd.dr.api.service.api.RecaptchaService;
import sk.isdd.dr.config.RecaptchaProperties;

@Service
public class RecaptchaServiceImpl implements RecaptchaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaServiceImpl.class);

    private final RecaptchaProperties recaptchaProperties;
    private final WebClient webClient;

    public RecaptchaServiceImpl(RecaptchaProperties recaptchaProperties) {
        this.recaptchaProperties = recaptchaProperties;
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
    }

    @Override
    public boolean verifyRecaptcha(String token) {
        if (!recaptchaProperties.isEnabled()) {
            return true;
        }

        if (token == null || token.trim().isEmpty()) {
            LOGGER.warn("reCAPTCHA token is null or empty");
            return false;
        }

        try {
            RecaptchaResponse response = callGoogleRecaptchaApi(token);

            if (!response.isSuccess()) {
                LOGGER.warn("reCAPTCHA verification failed. Errors: {}", response.getErrorCodes());
                return false;
            }

            return validateResponse(response);

        } catch (Exception e) {
            LOGGER.error("Error during reCAPTCHA verification", e);
            return false;
        }
    }

    private RecaptchaResponse callGoogleRecaptchaApi(String token) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("secret", recaptchaProperties.getSecretKey());
        formData.add("response", token);

        return webClient.post()
                .uri(recaptchaProperties.getVerifyUrl())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .bodyToMono(RecaptchaResponse.class)
                .timeout(Duration.ofSeconds(recaptchaProperties.getTimeoutSeconds()))
                .block();
    }

    private boolean validateResponse(RecaptchaResponse response) {
        // Check score
        if (response.getScore() < recaptchaProperties.getScoreThreshold()) {
            LOGGER.warn("reCAPTCHA score {} is below threshold {}", response.getScore(), recaptchaProperties.getScoreThreshold());
            return false;
        }

//        // Check action
//        String expectedAction = recaptchaProperties.getExpectedAction();
//        if (expectedAction != null && !expectedAction.equals(response.getAction())) {
//            LOGGER.warn("reCAPTCHA action '{}' does not match expected '{}'", response.getAction(), expectedAction);
//            return false;
//        }

        return true;
    }

    @Data
    private static class RecaptchaResponse {
        private boolean success;
        
        @JsonProperty("challenge_ts")
        private String challengeTs;
        
        private String hostname;
        
        @JsonProperty("error-codes")
        private String[] errorCodes;

        private Double score;

        private String action;
    }
}
