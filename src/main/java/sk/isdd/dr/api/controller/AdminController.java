package sk.isdd.dr.api.controller;

import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import sk.isdd.dr.common.validation.annotation.ValidRecaptcha;
import sk.isdd.dr.api.dto.EmailTemplate;
import sk.isdd.dr.api.dto.response.SuccessResponse;
import sk.isdd.dr.api.service.api.SubjectService;

@RestController
@RequestMapping("/admin")
@Validated
@Tag(name = "Admin Controller")
public class AdminController {

    private final SubjectService subjectService;

    public AdminController(SubjectService subjectService) {
        this.subjectService = subjectService;
    }

    @PreAuthorize("hasRole('admin')")
    @PatchMapping(path = "/email-template/{templateId}")
    public ResponseEntity<EmailTemplate> editEmailTemplate(@PathVariable Long templateId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(new EmailTemplate());
    }

    @PreAuthorize("hasRole('admin')")
    @GetMapping(path = "/email-template/{templateId}")
    public ResponseEntity<EmailTemplate> getEmailTemplate(@PathVariable Long templateId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(new EmailTemplate());
    }

    @PreAuthorize("hasRole('admin')")
    @GetMapping(path = "/export")
    public ResponseEntity<Resource> exportSubjects(@ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {

        Resource csv = subjectService.exportSubjects();

        if (csv == null) {
            return ResponseEntity.internalServerError()
                    .body(null);
        }

        try {
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, "attachment; filename=subjects.csv")
                    .contentType(MediaType.parseMediaType("text/csv"))
                    .contentLength(csv.contentLength())
                    .body(csv);

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(null);
        }

    }

    @PreAuthorize("hasRole('admin')")
    @PostMapping(path = "/email/not-verified", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SuccessResponse> sendNotVerifiedEmail(@ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(new SuccessResponse("OK"));
    }

}
