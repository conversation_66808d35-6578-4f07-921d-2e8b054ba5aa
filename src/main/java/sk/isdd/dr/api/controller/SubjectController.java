package sk.isdd.dr.api.controller;

import java.io.IOException;
import java.util.Objects;

import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.constraints.Positive;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.request.FilterSubjectsRequest;
import sk.isdd.dr.api.dto.response.FilterSubjectsResponse;
import sk.isdd.dr.api.dto.response.GetSubjectAuditLogsResponse;
import sk.isdd.dr.api.exception.business.ForbiddenException;
import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.auth.CurrentUser;
import sk.isdd.dr.common.enums.SubjectState;
import sk.isdd.dr.common.validation.annotation.ValidRecaptcha;

@RestController
@RequestMapping("/subject")
@Validated
@Tag(name = "Subject Controller")
public class SubjectController {

    private final SubjectService subjectService;

    private final LogService logService;

    public SubjectController(SubjectService subjectService, LogService logService) {
        this.subjectService = subjectService;
        this.logService = logService;
    }

    @PreAuthorize("hasAnyRole('client_pre_registered', 'client_registered', 'client')")
    @GetMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get details of logged subject", description = "", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Subject> getLoggedSubject(@AuthenticationPrincipal CurrentUser currentUser,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.getSubject(currentUser.getSubjectId()));
    }

    @PreAuthorize("hasAnyRole('client')")
    @GetMapping(path = "/attachment/{attachmentId}")
    public ResponseEntity<Resource> downloadLoggedSubjectAttachment(@AuthenticationPrincipal CurrentUser currentUser,
            @Positive @PathVariable Integer attachmentId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return downloadAttachment(currentUser.getSubjectId(), attachmentId);
    }

    @PreAuthorize("hasAnyRole('admin', 'partner_admin', 'coach', 'coach_admin')")
    @GetMapping(path = "/{subjectId}")
    public ResponseEntity<Subject> getSubjectById(@AuthenticationPrincipal CurrentUser currentUser,
            @PathVariable(name = "subjectId") Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {

        Subject subject = subjectService.getSubject(subjectId);

        boolean isAuthorized = currentUser.isAdmin() || currentUser.isCoachAdmin() || 
                (currentUser.isPartnerAdmin() && Objects.equals(subject.getPartner().getId(), currentUser.getPartnerId())) ||
                (currentUser.isCoach() && Objects.equals(subject.getCoach().getId(), currentUser.getId()));

        if (!isAuthorized) {
            throw new ForbiddenException();
        }

        return ResponseEntity.ok(subject);
    }

    @PreAuthorize("hasAnyRole('admin')")
    @DeleteMapping(path = "/{subjectId}")
    public ResponseEntity<Subject> deleteSubject(@Positive @PathVariable Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.deleteSubject(subjectId));
    }

    @PreAuthorize("hasAnyRole('admin', 'partner_admin', 'coach', 'coach_admin')")
    @PostMapping(path = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<FilterSubjectsResponse> filterSubjects(@AuthenticationPrincipal CurrentUser currentUser,
            @RequestBody FilterSubjectsRequest request,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {

        if (currentUser.isCoachAdmin()) {
            request.setIdNumber(null);
            request.setQuery(null);
            request.setPartnerId(null);
            request.setState(SubjectState.COACH_REQUESTED);
            
        } else if (currentUser.isCoach()) {
            request.setIdNumber(null);
            request.setQuery(null);
            request.setState(null);
            request.setPartnerId(null);
            request.setCoachId(currentUser.getId());
            
        } else if (currentUser.isPartnerAdmin()) {
            request.setPartnerId(currentUser.getPartnerId());
            if (request.getState() != SubjectState.REGISTERED && request.getState() != SubjectState.PRE_REGISTERED) {
                request.setState(SubjectState.REGISTERED);
            }
        }

        return ResponseEntity.ok(subjectService.filterSubjects(request));
    }

    @PreAuthorize("hasAnyRole('admin', 'partner_admin', 'coach', 'coach_admin')")
    @GetMapping(path = "/{subjectId}/attachment/{attachmentId}")
    public ResponseEntity<Resource> downloadAttachment(@PathVariable Integer subjectId,
            @PathVariable Integer attachmentId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {

        Resource response = subjectService.downloadAttachment(subjectId, attachmentId);

        try {
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + response.getFilename() + "\"")
                    .contentLength(response.contentLength())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(response);

        } catch (IOException e) {
            throw new SystemException("System exception");
        }
    }

    @PreAuthorize("hasRole('admin')")
    @GetMapping(path = "/{subjectId}/log/list")
    public ResponseEntity<GetSubjectAuditLogsResponse> getSubjectAuditLogs(@PathVariable Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        GetSubjectAuditLogsResponse response = new GetSubjectAuditLogsResponse();
        response.setAuditLogs(logService.getAuditLogs(subjectId));
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasRole('admin')")
    @PostMapping(path = "/{subjectId}/approve")
    public ResponseEntity<Subject> approveSubject(@PathVariable Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.verifySubject(subjectId, true));
    }

    @PreAuthorize("hasRole('admin')")
    @PostMapping(path = "/{subjectId}/decline")
    public ResponseEntity<Subject> declineSubject(@PathVariable Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.verifySubject(subjectId, false));
    }

    @PreAuthorize("hasAnyRole('admin')")
    @PostMapping(path = "/{subjectId}/shl/reports-approve")
    public ResponseEntity<Subject> approveSubjectSHLReport(@Positive @PathVariable Integer subjectId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.approveSHLReports(subjectId));
    }

}
