package sk.isdd.dr.api.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import sk.isdd.dr.common.validation.annotation.ValidRecaptcha;
import sk.isdd.dr.api.dto.response.GetChatbotMessageResponse;
import sk.isdd.dr.api.service.api.ChatbotService;

@RestController
@RequestMapping("/chatbot")
@Validated
@Tag(name = "Chatbot Controller")
public class ChatbotController {

    private final ChatbotService chatbotService;

    public ChatbotController(ChatbotService chatbotService) {
        this.chatbotService = chatbotService;
    }

    @GetMapping(path = "/chatgpt", produces = MediaType.APPLICATION_JSON_VALUE)
    @SecurityRequirements
    public ResponseEntity<GetChatbotMessageResponse> getChatGPTResponse(
            @RequestParam(name = "query", required = true) String query,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(chatbotService.getChatGPTResponse(query));
    }

    @GetMapping(path = "/claude", produces = MediaType.APPLICATION_JSON_VALUE)
    @SecurityRequirements
    public ResponseEntity<GetChatbotMessageResponse> getClaudeResponse(
            @RequestParam(name = "query", required = true) String query,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(chatbotService.getClaudeResponse(query));
    }

}
