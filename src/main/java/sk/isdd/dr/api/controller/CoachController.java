package sk.isdd.dr.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.response.GetUsersResponse;
import sk.isdd.dr.api.service.api.CoachService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.api.service.api.UserService;
import sk.isdd.dr.auth.CurrentUser;
import sk.isdd.dr.common.validation.annotation.PdfFile;
import sk.isdd.dr.common.validation.annotation.ValidRecaptcha;

@RestController
@RequestMapping("/coach")
@Validated
@Tag(name = "Coach Controller")
public class CoachController {

    @Autowired
    private CoachService coachService;

    private final SubjectService subjectService;

    private final UserService userService;

    public CoachController(SubjectService subjectService, UserService userService) {
        this.subjectService = subjectService;
        this.userService = userService;
    }

    @PreAuthorize("hasRole('client')")
    @PostMapping(path = "/request", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Subject> requestCoach(@AuthenticationPrincipal CurrentUser currentUser,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.requestCoach(currentUser.getSubjectId()));
    }

    @PreAuthorize("hasRole('coach_admin')")
    @PostMapping(path = "/assign/{coachId}/subject/{subjectId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Subject> assignCoach(@Positive @PathVariable Integer subjectId,
            @NotEmpty @PathVariable String coachId,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(subjectService.assignCoach(subjectId, coachId));
    }

    @PreAuthorize("hasAnyRole('coach', 'coach_admin')")
    @PutMapping(path = "/report/subject/{subjectId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Attachment> uploadCoachReport(@Positive @PathVariable Integer subjectId,
            @PdfFile @RequestParam(name = "report") MultipartFile reportFile,
            @ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {

        Attachment attachment = coachService.uploadCoachReport(subjectId, reportFile);
        return ResponseEntity.ok(attachment);
    }

    @PreAuthorize("hasRole('coach_admin')")
    @GetMapping(path = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GetUsersResponse> getCoaches(@ValidRecaptcha @RequestParam(name = "recaptcha_token") String recaptchaToken) {
        return ResponseEntity.ok(new GetUsersResponse(userService.getCoaches()));
    }

}
