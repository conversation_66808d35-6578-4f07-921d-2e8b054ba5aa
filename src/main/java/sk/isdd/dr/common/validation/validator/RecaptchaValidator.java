package sk.isdd.dr.common.validation.validator;

import org.springframework.beans.factory.annotation.Autowired;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import sk.isdd.dr.api.service.api.RecaptchaService;
import sk.isdd.dr.common.validation.annotation.ValidRecaptcha;

public class RecaptchaValidator implements ConstraintValidator<ValidRecaptcha, String> {

    @Autowired
    private RecaptchaService recaptchaService;

    @Override
    public boolean isValid(String token, ConstraintValidatorContext context) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        try {
            return recaptchaService.verifyRecaptcha(token);
        } catch (Exception e) {
            return false;
        }
    }
}
