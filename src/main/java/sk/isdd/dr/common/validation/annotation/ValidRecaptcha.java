package sk.isdd.dr.common.validation.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import sk.isdd.dr.common.validation.validator.RecaptchaValidator;

@Documented
@Constraint(validatedBy = RecaptchaValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidRecaptcha {

    String message() default "Invalid reCAPTCHA token";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
