<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.5</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>sk.isdd</groupId>
    <artifactId>dr-api</artifactId>

    <version>1.1.0-SNAPSHOT</version>
    <name>dr-api</name>
    <description>API for DR and NEET</description>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.mapstruct>1.5.5.Final</version.mapstruct>
        <version.flyway>11.7.2</version.flyway>
        <version.jgitflow>1.0-m5.1</version.jgitflow>
        <version.lombok>1.18.32</version.lombok>
        <version.cxf>4.0.4</version.cxf>
    </properties>

    <dependencies>

        <!-- Spring libs -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <!-- <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency> -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- DB -->

        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>3.0.11</version>
            <!-- <scope>runtime</scope> -->
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
            <version>${version.flyway}</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${version.flyway}</version>
        </dependency>

        <!-- Keycloak -->

        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-admin-client</artifactId>
            <version>26.0.6</version>
        </dependency>

        <!-- Cache -->

        <!-- <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency> -->

        <!-- Utils -->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${version.lombok}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${version.mapstruct}</version>
        </dependency>

        <!-- Files -->

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.29</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.7.1</version>
        </dependency>

        <!-- Emails -->

        <!-- <dependency>
            <groupId>com.github.spullara.mustache.java</groupId>
            <artifactId>compiler</artifactId>
            <version>0.9.10</version>
        </dependency> -->

        <!-- SOAP and XML -->

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>${version.cxf}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>dr-be-app</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${version.lombok}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${version.mapstruct}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>external.atlassian.jgitflow</groupId>
                <artifactId>jgitflow-maven-plugin</artifactId>
                <version>${version.jgitflow}</version>
                <configuration>
                    <pushFeatures>true</pushFeatures>
                    <pushReleases>true</pushReleases>
                    <pushHotfixes>true</pushHotfixes>
                    <suppressFastForward>true</suppressFastForward>
                    <scmCommentPrefix xml:space="preserve">[${plugin.artifactId}]: </scmCommentPrefix>
                    <flowInitContext>
                        <masterBranchName>main</masterBranchName>
                        <versionTagPrefix>${project.artifactId}-prod-</versionTagPrefix>
                    </flowInitContext>
                </configuration>
            </plugin>
            <!-- <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>${version.flyway}</version>
            </plugin> -->
            <plugin>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-codegen-plugin</artifactId>
                <version>${version.cxf}</version>
                <executions>
                    <execution>
                        <id>generate-sources</id>
                        <phase>generate-sources</phase>
                        <configuration>
                            <sourceRoot>${project.build.directory}/generated-sources/cxf</sourceRoot>
                            <wsdlOptions>
                                <wsdlOption>
                                    <wsdl>https://integration-talentcentral.eu.shl.com/integration/TMS/v1.0/Soap/TMSService.svc?wsdl</wsdl>
                                    <extraargs>
                                        <extraarg>-verbose</extraarg>
                                        <extraarg>-exsh</extraarg>
                                        <extraarg>true</extraarg>
                                        <extraarg>-autoNameResolution</extraarg>
                                    </extraargs>
                                </wsdlOption>
                            </wsdlOptions>
                        </configuration>
                        <goals>
                            <goal>wsdl2java</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- <profiles>
        <profile>
            <id>dev</id>
            
            <properties>
                <env>dev</env>
                <shlWsdl>
                    https://integration-talentcentral-uat.eu.shl.com/integration/TMS/v1.0/Soap/TMSService.svc?wsdl</shlWsdl>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env>prod</env>
                <shlWsdl>
                    https://integration-talentcentral.eu.shl.com/integration/TMS/v1.0/Soap/TMSService.svc?wsdl</shlWsdl>
            </properties>
        </profile>
    </profiles> -->

    <distributionManagement>
        <repository>
            <uniqueVersion>false</uniqueVersion>
            <id>nexus</id>
            <name>Releases</name>
            <url>https://nexus.isdd.sk/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <uniqueVersion>false</uniqueVersion>
            <id>nexus</id>
            <name>Snapshots</name>
            <url>https://nexus.isdd.sk/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>